import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ChangeDetectorRef, NgZone } from '@angular/core';
import { TickerItemComponent } from './ticker-item.component';
import { LogService } from '../../../core/services/log.service';
import { PlaylistItem } from '../../../core/models/playlist.model';

describe('TickerItemComponent', () => {
  let component: TickerItemComponent;
  let fixture: ComponentFixture<TickerItemComponent>;
  let mockLogService: jasmine.SpyObj<LogService>;
  let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;
  let mockNgZone: jasmine.SpyObj<NgZone>;

  beforeEach(async () => {
    mockLogService = jasmine.createSpyObj('LogService', ['info', 'warn', 'error']);
    mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']);
    mockNgZone = jasmine.createSpyObj('NgZone', ['run']);

    // Mock <PERSON>one.run to execute the callback immediately
    mockNgZone.run.and.callFake((fn: Function) => fn());

    await TestBed.configureTestingModule({
      imports: [TickerItemComponent],
      providers: [
        { provide: LogService, useValue: mockLogService },
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
        { provide: NgZone, useValue: mockNgZone }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TickerItemComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default state', () => {
    expect(component.isReady).toBe(false);
    expect(component.isAnimating).toBe(false);
    expect(component.isActive).toBe(false);
    expect(component.tickerText).toBe('');
  });

  it('should handle null item gracefully', fakeAsync(() => {
    spyOn(component.loaded, 'emit');

    component.item = null;
    component.ngOnInit();

    expect(component.tickerText).toBe('');
    expect(component.loaded.emit).toHaveBeenCalled();
    expect(mockLogService.warn).toHaveBeenCalledWith('No item provided to ticker component');
  }));

  it('should initialize ticker text from item content URL', fakeAsync(() => {
    const mockItem: PlaylistItem = {
      id: '1',
      name: 'Test Ticker',
      type: 'ticker',
      content: { url: 'Breaking News: Test ticker message' },
      duration: 10,
      schedule: null,
      settings: {
        transition: 'fade',
        transitionDuration: 500,
        scaling: 'fit'
      }
    };

    spyOn(component.loaded, 'emit');

    component.item = mockItem;
    component.preload = false;
    component.ngOnInit();
    tick(500); // Wait for ready timer

    expect(component.tickerText).toBe('Breaking News: Test ticker message');
    expect(component.isReady).toBe(true);
    expect(component.loaded.emit).toHaveBeenCalled();
    expect(mockLogService.info).toHaveBeenCalledWith(jasmine.stringMatching(/Ticker content loaded/));
  }));

  it('should become ready when not in preload mode', fakeAsync(() => {
    const mockItem: PlaylistItem = {
      id: '1',
      name: 'Test Ticker',
      type: 'ticker',
      content: { url: 'Test message' },
      duration: 5,
      schedule: null,
      settings: {
        transition: 'fade',
        transitionDuration: 500,
        scaling: 'fit'
      }
    };

    component.item = mockItem;
    component.preload = false;
    component.ngOnInit();
    tick(500); // Wait for ready timer

    expect(component.isReady).toBe(true);
    expect(mockNgZone.run).toHaveBeenCalled();
    expect(mockChangeDetectorRef.detectChanges).toHaveBeenCalled();
  }));

  it('should not become ready when in preload mode', fakeAsync(() => {
    const mockItem: PlaylistItem = {
      id: '1',
      name: 'Test Ticker',
      type: 'ticker',
      content: { url: 'Test message' },
      duration: 5,
      schedule: null,
      settings: {
        transition: 'fade',
        transitionDuration: 500,
        scaling: 'fit'
      }
    };

    component.item = mockItem;
    component.preload = true;
    component.ngOnInit();
    tick(500);

    expect(component.isReady).toBe(false);
  }));

  it('should emit ended event after duration completes', fakeAsync(() => {
    const mockItem: PlaylistItem = {
      id: '1',
      name: 'Test Ticker',
      type: 'ticker',
      content: { url: 'Test message' },
      duration: 2, // 2 seconds
      schedule: null,
      settings: {
        transition: 'fade',
        transitionDuration: 500,
        scaling: 'fit'
      }
    };

    spyOn(component.ended, 'emit');

    component.item = mockItem;
    component.isReady = true; // Set ready state
    component.startAnimation();

    // Fast forward 2 seconds
    tick(2000);

    expect(component.ended.emit).toHaveBeenCalled();
    expect(mockLogService.info).toHaveBeenCalledWith('Ticker duration completed');
  }));

  it('should stop animation when stopAnimation is called', () => {
    component.isAnimating = true;
    component.isActive = true;
    component.stopAnimation();

    expect(component.isAnimating).toBe(false);
    expect(component.isActive).toBe(false);
    expect(mockNgZone.run).toHaveBeenCalled();
    expect(mockChangeDetectorRef.detectChanges).toHaveBeenCalled();
    expect(mockLogService.info).toHaveBeenCalledWith('Stopping ticker animation');
  });

  it('should start animation when ready and startAnimation is called', () => {
    component.isReady = true;
    component.isAnimating = false;
    component.startAnimation();

    expect(component.isAnimating).toBe(true);
    expect(component.isActive).toBe(true);
    expect(mockNgZone.run).toHaveBeenCalled();
    expect(mockChangeDetectorRef.detectChanges).toHaveBeenCalled();
    expect(mockLogService.info).toHaveBeenCalledWith('Starting ticker animation');
  });

  it('should not start animation when not ready', () => {
    component.isReady = false;
    component.startAnimation();

    expect(component.isAnimating).toBe(false);
    expect(mockLogService.warn).toHaveBeenCalledWith('Cannot start animation - not ready or already animating');
  });

  it('should clean up timers on destroy', () => {
    component.ngOnDestroy();
    expect(mockLogService.info).toHaveBeenCalledWith('Ticker component destroying');
  });

  it('should return proper ticker styles with animation duration', () => {
    component.tickerText = 'Test message';
    const styles = component.getTickerStyle();

    expect(styles['--scroll-duration']).toBeDefined();
    expect(styles['--scroll-duration']).toMatch(/\d+s/);
  });

  it('should return empty styles when no ticker text', () => {
    component.tickerText = '';
    const styles = component.getTickerStyle();

    expect(styles).toEqual({});
  });
});
