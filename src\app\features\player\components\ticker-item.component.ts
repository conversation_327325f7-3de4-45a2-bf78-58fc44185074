// ticker-item.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectorRef, NgZone, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlaylistItem } from '../../../core/models/playlist.model';
import { LogService } from '../../../core/services/log.service';

@Component({
  selector: 'app-ticker-item',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="ticker-container" [class.ready]="isReady">
      @if (item && isReady) {
        <div class="ticker-content"
             #tickerContent
             [class.animate]="isAnimating"
             [ngStyle]="getTickerStyle()">
          {{ tickerText }}
        </div>
      }

      @if (!isReady) {
        <div class="loading-indicator">
          <div class="spinner"></div>
          <div class="loading-text">Loading ticker...</div>
        </div>
      }
    </div>
  `,
  styles: [`
    :host {
      display: block;
      width: 100%;
      height: 100%;
      overflow: hidden;
      background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    }

    .ticker-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      opacity: 0;
      transition: opacity 0.5s ease-in-out;
    }

    .ticker-container.ready {
      opacity: 1;
    }

    .ticker-content {
      white-space: nowrap;
      font-size: 2.5rem;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
      transform: translateX(100vw);
      will-change: transform;
      position: absolute;
      top: 50%;
      left: 0;
      transform-origin: center;
      margin-top: -1.25rem; /* Half of font-size for vertical centering */
    }

    .ticker-content.animate {
      animation: ticker-scroll var(--scroll-duration, 15s) linear infinite;
    }

    .loading-indicator {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 1rem;
      color: #ffffff;
    }

    .spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: #ffffff;
      animation: spin 1s ease-in-out infinite;
    }

    .loading-text {
      font-size: 1.2rem;
      font-weight: 500;
      opacity: 0.8;
    }

    @keyframes ticker-scroll {
      0% {
        transform: translateX(100vw);
      }
      100% {
        transform: translateX(-100%);
      }
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    /* Responsive font sizing */
    @media (max-width: 768px) {
      .ticker-content {
        font-size: 2rem;
        margin-top: -1rem;
      }
    }

    @media (max-width: 480px) {
      .ticker-content {
        font-size: 1.5rem;
        margin-top: -0.75rem;
      }
    }
  `]
})
export class TickerItemComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() item: PlaylistItem | null = null;
  @Input() preload = false;
  @Input() duration: number = 10;
  @Output() ended = new EventEmitter<void>();
  @Output() loaded = new EventEmitter<void>();

  tickerText: string = '';
  isReady = false;
  isAnimating = false;
  isActive = false;

  private timer: any;
  private readyTimer: any;
  private animationTimer: any;
  private cleanupTimer: any;

  constructor(
    private logService: LogService,
    private cdr: ChangeDetectorRef,
    private zone: NgZone
  ) {}

  ngOnInit(): void {
    this.logService.info('Ticker component initializing', {
      itemName: this.item?.name,
      preload: this.preload
    });

    this.reset();
    this.initializeContent();
  }

  ngAfterViewInit(): void {
    // Additional setup after view initialization if needed
    if (this.item && !this.preload) {
      this.makeReady();
    }
  }

  ngOnDestroy(): void {
    this.logService.info('Ticker component destroying');
    this.cleanup();
  }

  // Public methods for external control
  startAnimation(): void {
    if (!this.isReady || this.isAnimating) {
      this.logService.warn('Cannot start animation - not ready or already animating');
      return;
    }

    this.logService.info('Starting ticker animation');
    this.isActive = true;

    this.zone.run(() => {
      this.isAnimating = true;
      this.cdr.detectChanges();
    });

    this.startDurationTimer();
  }

  stopAnimation(): void {
    this.logService.info('Stopping ticker animation');
    this.isActive = false;

    this.clearTimers();

    this.zone.run(() => {
      this.isAnimating = false;
      this.cdr.detectChanges();
    });
  }

  // Private methods
  private reset(): void {
    this.tickerText = '';
    this.isReady = false;
    this.isAnimating = false;
    this.isActive = false;
    this.clearTimers();
  }

  private initializeContent(): void {
    if (!this.item) {
      this.logService.warn('No item provided to ticker component');
      this.emitLoaded();
      return;
    }

    // Extract ticker text from content URL
    this.tickerText = this.item.content.url || 'No content available';

    this.logService.info(`Ticker content loaded: "${this.tickerText.substring(0, 50)}${this.tickerText.length > 50 ? '...' : ''}"`);

    // For preload mode, just emit loaded immediately
    if (this.preload) {
      this.emitLoaded();
      return;
    }

    // For active mode, make ready after a short delay
    this.makeReady();
  }

  private makeReady(): void {
    this.clearTimers();

    this.readyTimer = setTimeout(() => {
      this.zone.run(() => {
        this.isReady = true;
        this.cdr.detectChanges();

        // Emit loaded event after becoming ready
        setTimeout(() => {
          this.emitLoaded();
        }, 100);
      });
    }, 300);
  }

  private emitLoaded(): void {
    this.logService.info('Ticker loaded event emitted');
    this.loaded.emit();
  }

  private startDurationTimer(): void {
    this.clearTimer();

    const duration = this.duration || this.item?.duration || 10;
    this.logService.info(`Starting ticker duration timer: ${duration}s`);

    this.timer = setTimeout(() => {
      this.logService.info('Ticker duration completed');
      this.stopAnimation();
      this.ended.emit();
    }, duration * 1000);
  }

  private clearTimer(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  private clearTimers(): void {
    [this.timer, this.readyTimer, this.animationTimer, this.cleanupTimer].forEach(timer => {
      if (timer) {
        clearTimeout(timer);
      }
    });

    this.timer = null;
    this.readyTimer = null;
    this.animationTimer = null;
    this.cleanupTimer = null;
  }

  private cleanup(): void {
    this.clearTimers();
    this.isAnimating = false;
    this.isActive = false;
    this.isReady = false;
  }

  getTickerStyle(): any {
    if (!this.tickerText) {
      return {};
    }

    // Calculate animation duration based on text length
    const baseSpeed = 100; // pixels per second
    const textLength = this.tickerText.length;
    const estimatedWidth = textLength * 20; // rough estimate
    const duration = Math.max(10, estimatedWidth / baseSpeed);

    return {
      '--scroll-duration': `${duration}s`
    };
  }
}